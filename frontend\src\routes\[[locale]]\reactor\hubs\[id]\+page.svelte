<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { fetchWithAuth, formatDate } from "$lib";
  import { Modal, LocalizedInput, LocalizedTextarea, ImageEditor } from "$lib/components";

  const i18n = {
    en: {
      _page: { title: "— Reactor of Commune" },
      head: "Head",
      createdOn: "Created on",
      editHub: "Edit Hub",
      uploadImage: "Upload Image",
      deleteImage: "Delete Image",
      communities: "Communities",
      noCommunities: "No communities found",
      loadingMoreCommunities: "Loading more communities...",
      editHubTitle: "Edit Hub",
      hubName: "Hub Name",
      hubDescription: "Hub Description",
      hubNamePlaceholder: "Enter hub name",
      hubDescriptionPlaceholder: "Enter hub description",
      save: "Save",
      cancel: "Cancel",
      saving: "Saving...",
      hubUpdatedSuccess: "Hub updated successfully!",
      errorUpdatingHub: "Failed to update hub",
      required: "This field is required",
      uploadImageTitle: "Upload Hub Image",
      upload: "Upload",
      uploading: "Uploading...",
      imageUploadedSuccess: "Image uploaded successfully!",
      errorUploadingImage: "Failed to upload image",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileType: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      uploadImageMaxSize: "Upload an image (JPG, PNG, WebP), max 5MB.",
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
      confirmDeleteImage: "Are you sure you want to delete this image?",
      deleteImageTitle: "Delete Image",
      delete: "Delete",
      deleting: "Deleting...",
      imageDeletedSuccess: "Image deleted successfully!",
      errorDeletingImage: "Failed to delete image",
    },
    ru: {
      _page: { title: "— Реактор Коммуны" },
      head: "Глава",
      createdOn: "Создан",
      editHub: "Редактировать хаб",
      uploadImage: "Загрузить изображение",
      deleteImage: "Удалить изображение",
      communities: "Сообщества",
      noCommunities: "Сообщества не найдены",
      loadingMoreCommunities: "Загружаем больше сообществ...",
      editHubTitle: "Редактировать хаб",
      hubName: "Название хаба",
      hubDescription: "Описание хаба",
      hubNamePlaceholder: "Введите название хаба",
      hubDescriptionPlaceholder: "Введите описание хаба",
      save: "Сохранить",
      cancel: "Отмена",
      saving: "Сохранение...",
      hubUpdatedSuccess: "Хаб успешно обновлен!",
      errorUpdatingHub: "Не удалось обновить хаб",
      required: "Это поле обязательно",
      uploadImageTitle: "Загрузить изображение хаба",
      upload: "Загрузить",
      uploading: "Загрузка...",
      imageUploadedSuccess: "Изображение загружено успешно!",
      errorUploadingImage: "Не удалось загрузить изображение",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      uploadImageMaxSize: "Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
      confirmDeleteImage: "Вы уверены, что хотите удалить это изображение?",
      deleteImageTitle: "Удалить изображение",
      delete: "Удалить",
      deleting: "Удаление...",
      imageDeletedSuccess: "Изображение удалено успешно!",
      errorDeletingImage: "Не удалось удалить изображение",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);
  const t = $derived(i18n[locale]);

  // State management
  let hub = $state(data.hub);
  let error = $state<string | null>(null);

  // Edit hub modal state
  let showEditModal = $state(false);
  let isUpdating = $state(false);
  let updateError = $state<string | null>(null);
  let updateSuccess = $state<string | null>(null);
  let hubName = $state<Common.Localizations>([]);
  let hubDescription = $state<Common.Localizations>([]);

  // Form change tracking for edit hub modal
  let initialHubName = $state<string>("");
  let initialHubDescription = $state<string>("");
  let isHubFormValuesChanged = $state(false);

  // Image upload modal state
  let showUploadModal = $state(false);
  let isUploading = $state(false);
  let uploadError = $state<string | null>(null);
  let uploadSuccess = $state<string | null>(null);
  let selectedFile = $state<File | null>(null);
  let previewUrl = $state<string | null>(null);

  // Image editor state
  let showImageEditor = $state(false);
  let editedFile = $state<File | null>(null);

  // Image delete modal state
  let showDeleteImageModal = $state(false);
  let isDeleting = $state(false);
  let deleteError = $state<string | null>(null);
  let deleteSuccess = $state<string | null>(null);

  // Communities state management
  let communities = $state(data.communities);
  let isLoadingMoreCommunities = $state(false);
  let currentCommunityPage = $state(1);
  let isHasMoreCommunities = $state(data.isHasMoreCommunities);
  let communitiesSentinelElement = $state<HTMLElement | null>(null);
  let communitiesError = $state<string | null>(null);

  // Edit hub functions
  function openEditModal() {
    showEditModal = true;
    hubName = [...hub.name];
    hubDescription = [...hub.description];

    // Set initial values for change tracking
    initialHubName = JSON.stringify(hub.name);
    initialHubDescription = JSON.stringify(hub.description);
    isHubFormValuesChanged = false;

    resetEditForm();
  }

  function closeEditModal() {
    showEditModal = false;
  }

  // Track hub form changes
  $effect(() => {
    if (showEditModal) {
      isHubFormValuesChanged =
        JSON.stringify(hubName) !== initialHubName ||
        JSON.stringify(hubDescription) !== initialHubDescription;
    }
  });

  function resetEditForm() {
    updateError = null;
    updateSuccess = null;
    isUpdating = false;
  }

  function validateEditForm(): boolean {
    if (!hubName.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }

    if (!hubDescription.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }

    return true;
  }

  async function handleUpdateHub() {
    if (!validateEditForm()) return;

    isUpdating = true;
    updateError = null;
    updateSuccess = null;

    try {
      await api.reactor.hub.patch({
        id: hub.id,
        name: hubName,
        description: hubDescription,
      });

      updateSuccess = t.hubUpdatedSuccess;

      setTimeout(() => {
        refresh();
      }, 1500);
    } catch (err) {
      updateError = err instanceof Error ? err.message : t.errorUpdatingHub;
      console.error(err);
    } finally {
      isUpdating = false;
    }
  }

  // Image upload functions
  function openUploadModal() {
    showUploadModal = true;
    resetUploadForm();
  }

  function closeUploadModal() {
    showUploadModal = false;
    selectedFile = null;
    previewUrl = null;
  }

  function resetUploadForm() {
    uploadError = null;
    uploadSuccess = null;
    isUploading = false;
    selectedFile = null;
    previewUrl = null;
    showImageEditor = false;
    editedFile = null;
  }

  function handleFileChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    uploadError = null;

    if (!files || files.length === 0) {
      selectedFile = null;
      previewUrl = null;
      return;
    }

    const file = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      uploadError = t.invalidFileType;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    // Validate file size
    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      uploadError = t.fileTooLarge;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    selectedFile = file;
    editedFile = null;
    showImageEditor = true;

    // Create preview URL for fallback
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    previewUrl = URL.createObjectURL(file);
  }

  async function handleUploadImage() {
    const fileToUpload = editedFile || selectedFile;

    if (!fileToUpload) {
      uploadError = t.pleaseSelectImage;
      return;
    }

    isUploading = true;
    uploadError = null;
    uploadSuccess = null;

    try {
      const formData = new FormData();
      formData.append("image", fileToUpload);

      const response = await fetchWithAuth(`/api/reactor/hub/${hub.id}/image`, {
        method: "PUT",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`${t.errorUploadingImage}: ${response.statusText}`);
      }

      uploadSuccess = t.imageUploadedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      uploadError = err instanceof Error ? err.message : t.errorUploadingImage;
      console.error(err);
    } finally {
      isUploading = false;
    }
  }

  // Image editor handlers
  function handleImageEditorSave(file: File) {
    editedFile = file;
    showImageEditor = false;

    // Update preview with edited image
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    const objectUrl = URL.createObjectURL(file);
    previewUrl = objectUrl;
  }

  function handleImageEditorCancel() {
    showImageEditor = false;
  }

  function handleEditImage() {
    if (selectedFile || editedFile) {
      showImageEditor = true;
    }
  }

  function handleSelectDifferentImage() {
    selectedFile = null;
    editedFile = null;
    previewUrl = null;
    showImageEditor = false;
    uploadError = null;

    // Clear the file input
    const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;
    if (imageInput) {
      imageInput.value = "";
    }
  }

  // Image delete functions
  function openDeleteImageModal() {
    showDeleteImageModal = true;
    resetDeleteForm();
  }

  function closeDeleteImageModal() {
    showDeleteImageModal = false;
  }

  function resetDeleteForm() {
    deleteError = null;
    deleteSuccess = null;
    isDeleting = false;
  }

  async function handleDeleteImage() {
    isDeleting = true;
    deleteError = null;
    deleteSuccess = null;

    try {
      const response = await fetchWithAuth(`/api/reactor/hub/${hub.id}/image`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`${t.errorDeletingImage}: ${response.statusText}`);
      }

      deleteSuccess = t.imageDeletedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      deleteError = err instanceof Error ? err.message : t.errorDeletingImage;
      console.error(err);
    } finally {
      isDeleting = false;
    }
  }

  async function loadMoreCommunities() {
    if (isLoadingMoreCommunities || !isHasMoreCommunities) return;

    isLoadingMoreCommunities = true;
    communitiesError = null;

    try {
      const newPage = currentCommunityPage + 1;

      const newCommunities = await api.reactor.community.list.get({
        pagination: { page: newPage },
        hubId: hub.id,
      });

      communities = [...communities, ...newCommunities];
      currentCommunityPage = newPage;

      isHasMoreCommunities = newCommunities.length === Consts.PAGE_SIZE;
    } catch (err) {
      communitiesError = err instanceof Error ? err.message : "Failed to load more communities";
      console.error(err);
    } finally {
      isLoadingMoreCommunities = false;
    }
  }

  function setupCommunitiesIntersectionObserver() {
    if (!communitiesSentinelElement) return null;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && isHasMoreCommunities && !isLoadingMoreCommunities) {
          loadMoreCommunities();
        }
      },
      {
        rootMargin: "100px",
        threshold: 0.1,
      },
    );

    observer.observe(communitiesSentinelElement);
    return observer;
  }

  // Setup intersection observer for communities infinite scroll
  onMount(() => {
    let observer: IntersectionObserver | null = null;

    const initObserver = () => {
      observer = setupCommunitiesIntersectionObserver();
    };

    // Try to setup observer immediately, or wait for element to be available
    if (communitiesSentinelElement) {
      initObserver();
    } else {
      setTimeout(initObserver, 100);
    }

    // Cleanup on component destroy
    return () => {
      observer?.disconnect();
    };
  });

  function refresh() {
    window.location.reload();
  }
</script>

<svelte:head>
  <title>{getAppropriateLocalization(hub.name) ?? "No name?"} {t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <!-- Hub Header -->
  <div class="row mb-4">
    <!-- Hub Image -->
    <div class="col-md-4 col-lg-3 mb-3">
      <div class="hub-image-container">
        {#if hub.image}
          <img
            src={`/images/${hub.image}`}
            alt={getAppropriateLocalization(hub.name) || "Hub"}
            class="hub-image"
          />
        {:else}
          <div class="hub-image-placeholder">
            <i class="bi bi-collection fs-1 text-muted"></i>
          </div>
        {/if}
      </div>

      <!-- Image Management Buttons -->
      {#if data.canEdit}
        <div class="mt-3 d-grid gap-2">
          <button class="btn btn-outline-primary btn-sm" onclick={openUploadModal}>
            <i class="bi bi-upload me-1"></i>
            {t.uploadImage}
          </button>
          {#if hub.image}
            <button class="btn btn-outline-danger btn-sm" onclick={openDeleteImageModal}>
              <i class="bi bi-trash me-1"></i>
              {t.deleteImage}
            </button>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Hub Info -->
    <div class="col-md-8 col-lg-9">
      <div class="d-flex justify-content-between align-items-start mb-3">
        <h1 class="mb-0">{getAppropriateLocalization(hub.name) || "Unknown Hub"}</h1>
        {#if data.canEdit}
          <button class="btn btn-primary" onclick={openEditModal}>
            <i class="bi bi-pencil me-1"></i>
            {t.editHub}
          </button>
        {/if}
      </div>

      <p class="text-muted mb-3 fs-5">
        {getAppropriateLocalization(hub.description) || ""}
      </p>

      <!-- Hub Metadata -->
      <div class="row g-3">
        <!-- Head User -->
        <div class="col-sm-6">
          <div class="d-flex align-items-center">
            <div class="me-3">
              {#if hub.headUser.image}
                <img
                  src={`/images/${hub.headUser.image}`}
                  alt={getAppropriateLocalization(hub.headUser.name)}
                  class="rounded-circle"
                  style="width: 48px; height: 48px; object-fit: cover;"
                />
              {:else}
                <div
                  class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                  style="width: 48px; height: 48px;"
                >
                  <i class="bi bi-person-fill text-white"></i>
                </div>
              {/if}
            </div>
            <div>
              <!-- <div class="small text-muted">{t.head}:</div> -->
              <a
                href={toLocaleHref(`/users/${hub.headUser.id}`)}
                class="fw-medium"
                style="text-decoration: none;"
              >
                {getAppropriateLocalization(hub.headUser.name)}
              </a>
            </div>
          </div>
        </div>

        <!-- Creation Date -->
        <!-- <div class="col-sm-6">
          <div class="small text-muted">{t.createdOn}:</div>
          <div class="fw-medium">{formatDate(hub.createdAt, locale)}</div>
        </div> -->
      </div>
    </div>
  </div>

  <!-- Communities Section -->
  <div class="mt-5">
    <h2 class="mb-4">{t.communities}</h2>

    {#if communities.length === 0}
      <div class="text-center py-5">
        <i class="bi bi-collection fs-1 text-muted mb-3"></i>
        <p class="text-muted">{t.noCommunities}</p>
      </div>
    {:else}
      <div class="row g-4">
        {#each communities as community (community.id)}
          <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm h-100">
              <div class="card-body d-flex flex-column">
                <!-- Community Image -->
                <div class="community-card-image-container mb-3">
                  {#if community.image}
                    <img
                      src={`/images/${community.image}`}
                      alt={getAppropriateLocalization(community.name) || "Community"}
                      class="community-card-image"
                    />
                  {:else}
                    <div class="community-card-image-placeholder">
                      <i class="bi bi-people fs-2 text-muted"></i>
                    </div>
                  {/if}
                </div>

                <!-- Community Name -->
                <h5 class="card-title mb-2">
                  <a
                    href={toLocaleHref(`/reactor/communities/${community.id}`)}
                    style="text-decoration: none;"
                  >
                    {getAppropriateLocalization(community.name) || "No name?"}
                  </a>
                </h5>

                <!-- Community Description -->
                <p class="card-text text-muted mb-3 flex-grow-1">
                  {getAppropriateLocalization(community.description) || ""}
                </p>

                <!-- Head User Info -->
                <div class="d-flex align-items-center mt-auto">
                  <div class="me-2">
                    {#if community.headUser.image}
                      <img
                        src={`/images/${community.headUser.image}`}
                        alt={getAppropriateLocalization(community.headUser.name)}
                        class="rounded-circle"
                        style="width: 32px; height: 32px; object-fit: cover;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                        style="width: 32px; height: 32px;"
                      >
                        <i class="bi bi-person-fill text-white"></i>
                      </div>
                    {/if}
                  </div>
                  <div class="small">
                    <!-- <div class="text-muted">{t.head}:</div> -->
                    <div class="fw-medium">
                      {getAppropriateLocalization(community.headUser.name)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>

      <!-- Infinite scroll sentinel element for communities -->
      {#if isHasMoreCommunities}
        <div bind:this={communitiesSentinelElement} class="text-center py-3 mt-4">
          {#if isLoadingMoreCommunities}
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">{t.loadingMoreCommunities}</span>
            </div>
            <p class="text-muted mt-2 mb-0">{t.loadingMoreCommunities}</p>
          {/if}
        </div>
      {/if}

      <!-- Communities error display -->
      {#if communitiesError}
        <div class="alert alert-warning mt-3" role="alert">
          {communitiesError}
        </div>
      {/if}
    {/if}
  </div>

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<!-- Edit Hub Modal -->
{#if data.canEdit}
  <Modal
    show={showEditModal}
    title={t.editHubTitle}
    onClose={closeEditModal}
    onSubmit={handleUpdateHub}
    submitText={isUpdating ? t.saving : t.save}
    cancelText={t.cancel}
    submitDisabled={isUpdating ||
      !hubName.some((item) => item.value.trim().length > 0) ||
      !hubDescription.some((item) => item.value.trim().length > 0) ||
      !isHubFormValuesChanged}
    isSubmitting={isUpdating}
  >
    {#if updateError}
      <div class="alert alert-danger mb-3">
        {updateError}
      </div>
    {/if}

    {#if updateSuccess}
      <div class="alert alert-success mb-3">
        {updateSuccess}
      </div>
    {/if}

    <form>
      <!-- Hub Name Input -->
      <LocalizedInput
        {locale}
        id="hub-name"
        label={t.hubName}
        placeholder={t.hubNamePlaceholder}
        required
        bind:value={hubName}
      />

      <!-- Hub Description Textarea -->
      <LocalizedTextarea
        {locale}
        label={t.hubDescription}
        placeholder={t.hubDescriptionPlaceholder}
        rows={4}
        required
        bind:value={hubDescription}
      />
    </form>
  </Modal>
{/if}

<!-- Upload Image Modal -->
{#if data.canEdit}
  <Modal
    show={showUploadModal}
    title={t.uploadImageTitle}
    onClose={closeUploadModal}
    onSubmit={showImageEditor ? undefined : handleUploadImage}
    submitText={isUploading ? t.uploading : t.upload}
    cancelText={t.cancel}
    submitDisabled={showImageEditor || (!selectedFile && !editedFile) || isUploading}
    isSubmitting={isUploading}
    size={showImageEditor ? "xl" : "lg"}
    showFooter={!showImageEditor}
  >
    {#if uploadSuccess}
      <div class="alert alert-success mb-3">
        {uploadSuccess}
      </div>
    {/if}

    {#if uploadError}
      <div class="alert alert-danger mb-3">
        {uploadError}
      </div>
    {/if}

    {#if showImageEditor && selectedFile}
      <ImageEditor
        imageFile={selectedFile}
        onSave={handleImageEditorSave}
        onCancel={handleImageEditorCancel}
        {locale}
        {editedFile}
      />
    {:else}
      <form>
        <div class="mb-3">
          <label for="imageInput" class="form-label">{t.pleaseSelectImage}</label>
          <input
            id="imageInput"
            type="file"
            class="form-control"
            accept=".jpg,.jpeg,.png,.webp"
            onchange={handleFileChange}
            disabled={isUploading}
          />
          <p class="form-text text-muted">
            {t.uploadImageMaxSize}
          </p>

          {#if previewUrl && !showImageEditor}
            <div class="mt-3">
              <div class="text-center mb-3">
                <img
                  src={previewUrl}
                  alt="Preview"
                  class="img-thumbnail"
                  style:max-height="200px"
                />
              </div>

              <div class="d-flex gap-2 justify-content-center">
                <button
                  type="button"
                  class="btn btn-outline-primary btn-sm"
                  onclick={handleEditImage}
                  disabled={isUploading}
                >
                  <i class="fas fa-edit"></i>
                  {t.editImage}
                </button>
                <button
                  type="button"
                  class="btn btn-outline-secondary btn-sm"
                  onclick={handleSelectDifferentImage}
                  disabled={isUploading}
                >
                  <i class="fas fa-image"></i>
                  {t.selectDifferentImage}
                </button>
              </div>
            </div>
          {/if}
        </div>
      </form>
    {/if}
  </Modal>
{/if}

<!-- Delete Image Modal -->
{#if data.canEdit && hub.image}
  <Modal
    show={showDeleteImageModal}
    title={t.deleteImageTitle}
    onClose={closeDeleteImageModal}
    onSubmit={handleDeleteImage}
    submitText={isDeleting ? t.deleting : t.delete}
    cancelText={t.cancel}
    submitDisabled={isDeleting}
    isSubmitting={isDeleting}
  >
    {#if deleteSuccess}
      <div class="alert alert-success mb-3">
        {deleteSuccess}
      </div>
    {/if}

    {#if deleteError}
      <div class="alert alert-danger mb-3">
        {deleteError}
      </div>
    {/if}

    <p>{t.confirmDeleteImage}</p>
  </Modal>
{/if}

<style>
  .hub-image-container {
    width: 100%;
    height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
  }

  .hub-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .hub-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  @media (max-width: 768px) {
    .hub-image-container {
      height: 250px;
    }
  }

  @media (max-width: 576px) {
    .hub-image-container {
      height: 200px;
    }
  }

  /* Community card styles */
  .community-card-image-container {
    width: 100%;
    height: 120px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
  }

  .community-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .community-card-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }
</style>
