import * as client_hooks from '../../../src/hooks.client.ts';


export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29'),
	() => import('./nodes/30'),
	() => import('./nodes/31'),
	() => import('./nodes/32')
];

export const server_loads = [3];

export const dictionary = {
		"/admin": [6,[2]],
		"/admin/invites": [7,[2]],
		"/[[locale]]/auth": [24,[3]],
		"/[[locale]]/(index)/communes": [9,[3,4]],
		"/[[locale]]/(index)/communes/invitations": [10,[3,4]],
		"/[[locale]]/(index)/communes/join-requests": [11,[3,4]],
		"/[[locale]]/(index)/communes/[id]": [12,[3,4]],
		"/[[locale]]/(index)/communes/[id]/invitations": [13,[3,4]],
		"/[[locale]]/(index)/communes/[id]/join-requests": [14,[3,4]],
		"/[[locale]]/(index)/new-calendar": [15,[3,4]],
		"/[[locale]]/(index)/new-english": [16,[3,4]],
		"/[[locale]]/(index)/profile": [17,[3,4]],
		"/[[locale]]/reactor": [25,[3,5]],
		"/[[locale]]/reactor/communities": [26,[3,5]],
		"/[[locale]]/reactor/communities/[id]": [27,[3,5]],
		"/[[locale]]/reactor/hubs": [28,[3,5]],
		"/[[locale]]/reactor/hubs/[id]": [29,[3,5]],
		"/[[locale]]/reactor/[id]": [30,[3,5]],
		"/[[locale]]/(index)/rules": [18,[3,4]],
		"/[[locale]]/test/editor": [31,[3]],
		"/[[locale]]/test/tag": [32,[3]],
		"/[[locale]]/(index)/the-law": [19,[3,4]],
		"/[[locale]]/(index)/users": [20,[3,4]],
		"/[[locale]]/(index)/users/[id]": [21,[3,4]],
		"/[[locale]]/(index)/users/[id]/feedback": [22,[3,4]],
		"/[[locale]]/(index)/users/[id]/karma": [23,[3,4]],
		"/[[locale]]/(index)": [8,[3,4]]
	};

export const hooks = {
	handleError: client_hooks.handleError || (({ error }) => { console.error(error) }),
	init: client_hooks.init,
	reroute: (() => {}),
	transport: {}
};

export const decoders = Object.fromEntries(Object.entries(hooks.transport).map(([k, v]) => [k, v.decode]));

export const hash = false;

export const decode = (type, value) => decoders[type](value);

export { default as root } from '../root.js';