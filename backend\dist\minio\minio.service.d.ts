import { OnModuleInit } from "@nestjs/common";
import { ConfigService } from "src/config/config.service";
export interface FileInfo {
    originalname: string;
    buffer: Buffer;
    mimetype: string;
    size: number;
}
export declare const minioImageEntities: readonly ["commune", "user", "reactor-hub", "reactor-community"];
export type MinioImageEntity = (typeof minioImageEntities)[number];
export declare class MinioService implements OnModuleInit {
    private readonly configService;
    private client;
    private readonly logger;
    private readonly buckets;
    constructor(configService: ConfigService);
    onModuleInit(): void;
    private initializeBuckets;
    getImageObjectName(entityId: string, index?: number): string;
    uploadImage(file: FileInfo, entity: MinioImageEntity, entityId: string, index?: number): Promise<string>;
    uploadPostImage(id: string, file: FileInfo): Promise<string>;
    uploadFile(file: FileInfo, bucket: string, objectName: string): Promise<string>;
    deleteImage(entity: MinioImageEntity, entityId: string, index?: number): Promise<void>;
    deleteFile(bucket: string, objectName: string): Promise<void>;
}
