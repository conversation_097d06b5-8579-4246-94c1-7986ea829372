<script lang="ts">
  import type { Common } from "@commune/api";
  import { Consts } from "@commune/api";
  import { ImageEditor } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    onSave?: (file: File) => void;
    onCancel?: () => void;
    disabled?: boolean;
    showPreview?: boolean;
    maxHeight?: string;
    inputId?: string;
    label?: string;
    helpText?: string;
    editButtonText?: string;
    selectDifferentButtonText?: string;
    initialFile?: File | null;
    showImageEditor?: boolean;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      pleaseSelectImage: "Please select an image",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
    },
    ru: {
      pleaseSelectImage: "Пожалуйста, выберите изображение",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
    },
  };

  const {
    locale,
    onSave,
    onCancel,
    disabled = false,
    showPreview = true,
    maxHeight = "200px",
    inputId = "imageInput",
    label,
    helpText,
    editButtonText,
    selectDifferentButtonText,
    initialFile = null,
    showImageEditor: initialShowImageEditor = false,
  }: Props = $props();

  const t = $derived(i18n[locale]);

  let selectedFile = $state<File | null>(initialFile);
  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let showImageEditor = $state(initialShowImageEditor);
  let editedFile = $state<File | null>(null);

  // Create preview URL when file changes
  $effect(() => {
    if (selectedFile) {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      previewUrl = URL.createObjectURL(selectedFile);
    } else {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        previewUrl = null;
      }
    }
  });

  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;

    error = "";

    if (!files || files.length === 0) {
      selectedFile = null;
      return;
    }

    const file = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      error = t.invalidFileTypeError;
      selectedFile = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    // Validate file size
    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      error = t.fileTooLarge;
      selectedFile = null;
      // Clear the input immediately if validation fails
      target.value = "";
      return;
    }

    selectedFile = file;
    editedFile = null;
    showImageEditor = true;
  };

  const handleImageEditorSave = (file: File) => {
    editedFile = file;
    showImageEditor = false;
    onSave?.(file);
  };

  const handleImageEditorCancel = () => {
    showImageEditor = false;
    onCancel?.();
  };

  const handleEditImage = () => {
    if (selectedFile || editedFile) {
      showImageEditor = true;
    }
  };

  const handleSelectDifferentImage = () => {
    selectedFile = null;
    editedFile = null;
    showImageEditor = false;
    error = "";

    // Clear the file input
    const imageInput = document.getElementById(inputId) as HTMLInputElement | null;
    if (imageInput) {
      imageInput.value = "";
    }
  };

  // Expose the current file for parent components
  export const getCurrentFile = () => editedFile || selectedFile;
  export const getError = () => error;
  export const hasFile = () => !!(editedFile || selectedFile);
  export const reset = () => {
    selectedFile = null;
    editedFile = null;
    showImageEditor = false;
    error = "";
    const imageInput = document.getElementById(inputId) as HTMLInputElement | null;
    if (imageInput) {
      imageInput.value = "";
    }
  };
</script>

{#if showImageEditor && selectedFile}
  <ImageEditor
    imageFile={selectedFile}
    onSave={handleImageEditorSave}
    onCancel={handleImageEditorCancel}
    {locale}
    {editedFile}
  />
{:else}
  <div class="mb-3">
    {#if label}
      <label for={inputId} class="form-label">{label}</label>
    {:else}
      <label for={inputId} class="form-label">{t.pleaseSelectImage}</label>
    {/if}

    <input
      id={inputId}
      type="file"
      class="form-control"
      accept=".jpg,.jpeg,.png,.webp"
      onchange={handleFileChange}
      {disabled}
    />

    {#if helpText}
      <p class="form-text text-muted">{helpText}</p>
    {:else}
      <p class="form-text text-muted">{t.uploadImageMaxSize}</p>
    {/if}

    {#if error}
      <div class="alert alert-danger mt-2 mb-0">
        {error}
      </div>
    {/if}

    {#if showPreview && previewUrl && !showImageEditor}
      <div class="mt-3">
        <div class="text-center mb-3">
          <img src={previewUrl} alt="Preview" class="img-thumbnail" style:max-height={maxHeight} />
        </div>

        <div class="d-flex gap-2 justify-content-center">
          <button
            type="button"
            class="btn btn-outline-primary btn-sm"
            onclick={handleEditImage}
            {disabled}
          >
            <i class="fas fa-edit"></i>
            {editButtonText || t.editImage}
          </button>
          <button
            type="button"
            class="btn btn-outline-secondary btn-sm"
            onclick={handleSelectDifferentImage}
            {disabled}
          >
            <i class="fas fa-image"></i>
            {selectDifferentButtonText || t.selectDifferentImage}
          </button>
        </div>
      </div>
    {/if}
  </div>
{/if}
