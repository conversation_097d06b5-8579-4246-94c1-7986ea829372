import type { Infer } from "./types";

import { z } from "zod";
import {
    id,
    searchIds,
    searchQuery,
    maybeImageUrl,
    createdAt,
    updatedAt,
    deletedAt,
    JsonStringToObject,
    LocalizationsSchema,
    PaginationSchema,
} from "./common";
import { userName } from "./user";

// communes

export type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;
export const CommuneMemberTypeSchema = z.enum(["user"]);

export const communeName = LocalizationsSchema.min(1);
export const communeDescription = LocalizationsSchema;

export const communeMemberActorType = CommuneMemberTypeSchema;
export const communeMemberName = z.union([userName, communeName]);

export type TransferHeadStatusInput = Infer<typeof TransferHeadStatusInputSchema>;
export const TransferHeadStatusInputSchema = z.object({
    communeId: id,
    newHeadUserId: id,
});

export type GetCommunesInput = Infer<typeof GetCommunesInputSchema>;
export const GetCommunesInputSchema = z
    .object({
        pagination: PaginationSchema,

        ids: searchIds,
        query: searchQuery,
        userId: id,
    })
    .partial();

export type GetCommuneOutput = Infer<typeof GetCommuneOutputSchema>;
export const GetCommuneOutputSchema = z.object({
    id,

    name: communeName,
    description: communeDescription,

    headMember: z.object({
        actorType: communeMemberActorType,
        actorId: id,

        name: communeMemberName,

        image: maybeImageUrl,
    }),

    memberCount: z.number().int().positive(),

    image: maybeImageUrl,

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),
})

export type GetCommunesOutput = Infer<typeof GetCommunesOutputSchema>;
export const GetCommunesOutputSchema = z.array(GetCommuneOutputSchema);

export type CreateCommuneInput = Infer<typeof CreateCommuneInputSchema>;
export const CreateCommuneInputSchema = z.object({
    headUserId: id.optional(),

    name: communeName,
    description: communeDescription,
});

export type UpdateCommuneInput = Infer<typeof UpdateCommuneInputSchema>;
export const UpdateCommuneInputSchema = z
    .object({
        id,

        name: communeName.optional(),
        description: communeDescription.optional(),
    });

// commune members

export type GetCommuneMembersInput = Infer<typeof GetCommuneMembersInputSchema>;
export const GetCommuneMembersInputSchema = z.object({
    pagination: PaginationSchema,

    communeId: id,
});

export type GetCommuneMemberOutput = Infer<typeof GetCommuneMemberOutputSchema>;
export const GetCommuneMemberOutputSchema = z.object({
    id,

    actorType: communeMemberActorType,
    actorId: id,

    name: communeMemberName,

    image: maybeImageUrl,

    createdAt,
    deletedAt: deletedAt.nullable(),
});

export type GetCommuneMembersOutput = Infer<typeof GetCommuneMembersOutputSchema>;
export const GetCommuneMembersOutputSchema = z.array(GetCommuneMemberOutputSchema);

export type CreateCommuneMemberInput = Infer<typeof CreateCommuneMemberInputSchema>;
export const CreateCommuneMemberInputSchema = z.object({
    communeId: id,
    userId: id,
});

// commune invitations

export type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;
export const CommuneInvitationStatusSchema = z.enum(["pending", "accepted", "rejected", "expired"]);

export type GetCommuneInvitationsInput = Infer<typeof GetCommuneInvitationsInputSchema>;
export const GetCommuneInvitationsInputSchema = z.object({
    pagination: PaginationSchema,

    communeId: id.optional(),
});

export type GetCommuneInvitationsOutput = Infer<typeof GetCommuneInvitationsOutputSchema>;
export const GetCommuneInvitationsOutputSchema = z.array(
    z.object({
        id,

        communeId: id,
        userId: id,

        status: CommuneInvitationStatusSchema,

        createdAt: z.date(),
        updatedAt: z.date(),
    }),
);

export type CreateCommuneInvitationInput = Infer<typeof CreateCommuneInvitationInputSchema>;
export const CreateCommuneInvitationInputSchema = z.object({
    communeId: id,
    userId: id,
});

// commune join requests

export type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;
export const CommuneJoinRequestStatusSchema = z.enum(["pending", "accepted", "rejected"]);

export type GetCommuneJoinRequestsInput = Infer<typeof GetCommuneJoinRequestsInputSchema>;
export const GetCommuneJoinRequestsInputSchema = z.object({
    pagination: PaginationSchema,

    communeId: id.optional(),
});

export type GetCommuneJoinRequestsOutput = Infer<typeof GetCommuneJoinRequestsOutputSchema>;
export const GetCommuneJoinRequestsOutputSchema = z.array(
    z.object({
        id,

        communeId: id,
        userId: id,

        status: CommuneJoinRequestStatusSchema,

        createdAt: z.date(),
        updatedAt: z.date(),
    })
);

export type CreateCommuneJoinRequestInput = Infer<typeof CreateCommuneJoinRequestInputSchema>;
export const CreateCommuneJoinRequestInputSchema = z.object({
    communeId: id,
    userId: id,
});
