"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneCore = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let CommuneCore = class CommuneCore {
    isHeadMember(commune, userId) {
        return commune.members.some((member) => member.deletedAt === null &&
            member.isHead &&
            member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
    isMember(commune, userId) {
        return commune.members.some((member) => member.deletedAt === null &&
            member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
};
exports.CommuneCore = CommuneCore;
exports.CommuneCore = CommuneCore = __decorate([
    (0, common_1.Injectable)()
], CommuneCore);
//# sourceMappingURL=commune.core.js.map