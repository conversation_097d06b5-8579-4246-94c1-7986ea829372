<script lang="ts">
  import type { Common } from "@commune/api";

  import { preventDefault } from "$lib";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { Modal, LocalizedInput, LocalizedTextarea } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    toLocaleHref: (href: string) => string;
  }

  const i18n = {
    en: {
      createNewCommune: "Create New Commune",
      communeCreatedSuccess: "Commune created successfully!",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      create: "Create",
      creating: "Creating...",
      provideName: "Please provide a name for the commune.",
      failedToCreate: "Failed to create commune",
      unexpectedError: "An unexpected error occurred. Please try again.",
    },
    ru: {
      createNewCommune: "Создать новую коммуну",
      communeCreatedSuccess: "Коммуна успешно создана!",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      create: "Создать",
      creating: "Создание...",
      provideName: "Пожалуйста, укажите название коммуны.",
      failedToCreate: "Не удалось создать коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
    },
  };

  const { fetcher: api } = getClient();

  const { show, locale, onHide, toLocaleHref }: Props = $props();

  const t = $derived(i18n[locale]);

  let fileInputRef = $state<HTMLInputElement | null>(null);
  let communeName = $state<Common.Localizations>([]);
  let communeDescription = $state<Common.Localizations>([]);
  let error = $state<string>("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  function handleClose() {
    onHide();
    communeName = [];
    communeDescription = [];
    error = "";
    submitSuccess = false;

    if (fileInputRef) {
      fileInputRef.value = "";
    }
  }

  async function handleSubmit() {
    error = "";

    // Validate required fields
    if (!communeName.some((item) => item.value.trim().length)) {
      error = t.provideName;
      return;
    }

    isSubmitting = true;

    try {
      const { id } = await api.commune.post({
        name: communeName,
        description: communeDescription,
      });

      submitSuccess = true;

      setTimeout(() => {
        goto(toLocaleHref(`/communes/${id}`));
      }, 1500);
    } catch (err) {
      console.error("Error creating commune:", err);
      error = t.unexpectedError;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<Modal
  {show}
  title={t.createNewCommune}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.creating : t.create}
  cancelText={t.cancel}
  submitDisabled={!communeName.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.communeCreatedSuccess}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form onsubmit={preventDefault(handleSubmit)}>
    <LocalizedInput
      id="communeName"
      label={t.name}
      placeholder={t.enterCommuneName}
      required={true}
      {locale}
      bind:value={communeName}
    />

    <LocalizedTextarea
      id="communeDescription"
      label={t.description}
      placeholder={t.enterCommuneDescription}
      rows={4}
      {locale}
      bind:value={communeDescription}
    />
  </form>
</Modal>
