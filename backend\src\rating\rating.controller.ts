import { Controller } from "@nestjs/common";
import { getServer } from "src/acrpc";
import { RatingService } from "./rating.service";

@Controller("rating")
export class RatingController {
    constructor(private readonly ratingService: RatingService) {
        const acrpcServer = getServer();

        acrpcServer.register({
            rating: {
                karma: {
                    list: {
                        get: async (input) => {
                            const points =
                                await this.ratingService.getKarmaPoints(input);

                            return points.map((p) => ({
                                id: p.id,
                                author: {
                                    ...p.sourceUser,
                                    image: p.sourceUser.image?.url ?? null,
                                },
                                quantity: p.quantity,
                                comment: p.comment,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.ratingService.spendKarmaPoint(
                            input,
                            metadata.user,
                        ),
                },
                feedback: {
                    list: {
                        get: async (input) => {
                            const feedbacks =
                                await this.ratingService.getUserFeedbacks(
                                    input,
                                );

                            return feedbacks.map((f) => ({
                                id: f.id,
                                author: f.isAnonymous
                                    ? null
                                    : {
                                          ...f.sourceUser,
                                          image:
                                              f.sourceUser.image?.url ?? null,
                                      },
                                isAnonymous: f.isAnonymous,
                                value: f.value,
                                text: f.text,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.ratingService.createUserFeedback(
                            input,
                            metadata.user,
                        ),
                },
                summary: {
                    get: (input) => this.ratingService.getUserSummary(input),
                },
            },
        });
    }

    // @Get("karma")
    // async getKarmaPoints(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(new ZodPipe(Rating.GetKarmaPointsInputSchema))
    //     input: Rating.GetKarmaPointsInput,
    // ) {
    //     const points = await this.ratingService.getKarmaPoints(input);

    //     return Common.parseInput(
    //         Rating.GetKarmaPointsOutputSchema,
    //         points.map((p) => ({
    //             id: p.id,
    //             author: {
    //                 ...p.sourceUser,
    //                 image: p.sourceUser.image?.url ?? null,
    //             },
    //             quantity: p.quantity,
    //             comment: p.comment,
    //         })),
    //     );
    // }

    // @Post("karma")
    // async spendKarmaPoint(
    //     @Body(new ZodPipe(Rating.SpendKarmaPointInputSchema))
    //     input: Rating.SpendKarmaPointInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const result = await this.ratingService.spendKarmaPoint(input, user);

    //     return Common.parseInput(Common.ObjectWithIdSchema, result);
    // }

    // @Get("feedback")
    // async getUserFeedbacks(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(new ZodPipe(Rating.GetUserFeedbacksInputSchema))
    //     input: Rating.GetUserFeedbacksInput,
    // ) {
    //     const feedbacks = await this.ratingService.getUserFeedbacks(input);

    //     return Common.parseInput(
    //         Rating.GetUserFeedbacksOutputSchema,
    //         feedbacks.map((f) => ({
    //             id: f.id,
    //             author: f.isAnonymous
    //                 ? null
    //                 : {
    //                       ...f.sourceUser,
    //                       image: f.sourceUser.image?.url ?? null,
    //                   },
    //             isAnonymous: f.isAnonymous,
    //             value: f.value,
    //             text: f.text,
    //         })),
    //     );
    // }

    // @Post("feedback")
    // async createUserFeedback(
    //     @Body(new ZodPipe(Rating.CreateUserFeedbackInputSchema))
    //     input: Rating.CreateUserFeedbackInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const result = await this.ratingService.createUserFeedback(input, user);

    //     return Common.parseInput(Common.ObjectWithIdSchema, result);
    // }

    // @Get("summary")
    // async getUserSummary(
    //     @Query(new ZodPipe(Rating.GetUserSummaryInputSchema))
    //     input: Rating.GetUserSummaryInput,
    // ) {
    //     const summary = await this.ratingService.getUserSummary(input);

    //     return Common.parseInput(Rating.GetUserSummaryOutputSchema, summary);
    // }
}
