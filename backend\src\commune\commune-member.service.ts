import { Common, Commune } from "@commune/api";
import { CommuneMember } from "@prisma/client";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { getError } from "src/common/errors";
import { toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";

@Injectable()
export class CommuneMemberService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly communeCore: CommuneCore,
    ) {}

    async hydrateMembers(
        members: CommuneMember[],
    ): Promise<Commune.GetCommuneMembersOutput> {
        const userIds = members
            .filter((member) => member.actorType === "user")
            .map((member) => member.actorId);

        const users = await this.prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            include: {
                name: true,
                image: true,
            },
        });

        const userMap = new Map(users.map((user) => [user.id, user]));

        return members.map((member) => {
            switch (member.actorType) {
                case "user":
                    return {
                        ...member,
                        name: userMap.get(member.actorId)!.name,
                        image: userMap.get(member.actorId)!.image?.url ?? null,
                    };
            }
        });
    }

    async getCommuneMembers(input: Commune.GetCommuneMembersInput) {
        const members = await this.prisma.communeMember.findMany({
            ...toPrismaPagination(input.pagination),
            where: {
                communeId: input.communeId,

                deletedAt: null,
            },
            orderBy: [{ isHead: "desc" }, { createdAt: "asc" }],
        });

        return await this.hydrateMembers(members);
    }

    async createCommuneMember(input: Commune.CreateCommuneMemberInput) {
        return await this.prisma.communeMember.create({
            data: {
                actorType: "user",
                actorId: input.userId,
                commune: {
                    connect: {
                        id: input.communeId,
                        deletedAt: null,
                    },
                },
            },
        });
    }

    async deleteCommuneMember(input: Common.ObjectWithId, user: CurrentUser) {
        const member = await this.prisma.communeMember.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: member.communeId },
            include: {
                members: true,
            },
        });

        const isNotAdmin = !user.isAdmin;
        const isNotHeadMember = !this.communeCore.isHeadMember(
            commune,
            user.id,
        );
        const isNotSelf =
            member.actorType === "user" && member.actorId !== user.id;

        if (isNotAdmin && isNotHeadMember && isNotSelf) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member_or_self"),
            );
        }

        return await this.prisma.communeMember.update({
            where: { id: input.id },
            data: { deletedAt: new Date() },
        });
    }
}
