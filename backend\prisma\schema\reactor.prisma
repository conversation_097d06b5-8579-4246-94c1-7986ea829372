// reactor-post
model ReactorPost {
    @@map("reactor_posts")

    id String @id @default(nanoid())

    tags Tag[] @relation("reactor_post_tags")

    authorId String @map("author_id")
    author   User   @relation("user_reactor_posts", fields: [authorId], references: [id])

    hubId String?     @map("hub_id")
    hub   ReactorHub? @relation("reactor_hub_posts", fields: [hubId], references: [id])

    communityId String?           @map("community_id")
    community   ReactorCommunity? @relation("reactor_community_posts", fields: [communityId], references: [id])

    title Localization[] @relation("reactor_post_title")
    body  Localization[] @relation("reactor_post_body")

    images Image[] @relation("reactor_post_images")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@index([authorId])
    @@index([hubId])
    @@index([communityId])
}

model ReactorPostInternalNumber {
    @@map("reactor_post_internal_numbers")

    id String @id @default(nanoid())

    postId String @map("post_id") @unique

    internalNumber Int @map("internal_number")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
}

// reactor-comment
model ReactorComment {
    @@map("reactor_comments")

    id String @id @default(nanoid())

    authorId String @map("author_id")
    author   User   @relation("user_reactor_comments", fields: [authorId], references: [id])

    postId String @map("post_id")

    path           String
    internalNumber Int    @map("internal_number")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    body Localization[] @relation("reactor_comment_body")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@unique([postId, internalNumber])
    @@unique([postId, path])
}

// reactor-entity-type
enum ReactorEntityType {
    @@map("reactor_entity_type")

    post
    comment
}

enum ReactorRatingType {
    @@map("reactor_rating_type")

    like
    dislike
}

model ReactorRating {
    @@map("reactor_ratings")

    id String @id @default(nanoid())

    userId String @map("user_id")
    user   User   @relation("user_reactor_ratings", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")  

    type ReactorRatingType

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([entityType, entityId, userId])
}

model ReactorUsefulness {
    @@map("reactor_usefulnesses")

    id String @id @default(nanoid())

    userId String @map("user_id")
    user   User   @relation("user_reactor_usefulnesses", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")  

    value Int

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([entityType, entityId, userId])
}

// reactor-hub
model ReactorHub {
    @@map("reactor_hubs")

    id String @id @default(nanoid())

    headUserId String @map("head_user_id")
    headUser   User   @relation("user_reactor_hubs", fields: [headUserId], references: [id])

    imageId String? @map("image_id")
    image   Image?  @relation("reactor_hub_image", fields: [imageId], references: [id])

    name        Localization[] @relation("reactor_hub_name")
    description Localization[] @relation("reactor_hub_description")

    communities ReactorCommunity[] @relation("reactor_hub_communities")
    posts       ReactorPost[]      @relation("reactor_hub_posts")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// reactor-community
model ReactorCommunity {
    @@map("reactor_communities")

    id String @id @default(nanoid())

    hubId String?     @map("hub_id")
    hub   ReactorHub? @relation("reactor_hub_communities", fields: [hubId], references: [id])

    headUserId String @map("head_user_id")
    headUser   User   @relation("user_reactor_communities", fields: [headUserId], references: [id])

    imageId String? @map("image_id")
    image   Image?  @relation("reactor_community_image", fields: [imageId], references: [id])

    name        Localization[] @relation("reactor_community_name")
    description Localization[] @relation("reactor_community_description")

    posts ReactorPost[] @relation("reactor_community_posts")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@index([hubId])
}

// reactor-lens
model ReactorLens {
    @@map("reactor_lenses")

    id String @id @default(nanoid())

    userId String @map("user_id")
    user   User   @relation("user_reactor_lenses", fields: [userId], references: [id])

    name String

    code String
    sql  String

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
