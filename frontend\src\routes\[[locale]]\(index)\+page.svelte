<script lang="ts">
  const i18n = {
    en: {
      _page: {
        title: "Commune",
      },

      hero: {
        title: "Commune",
        subtitle: "Construction through technological progress",
      },

      roadmap: {
        title: "Roadmap",
        subtitle: "Building Our Common Future Together",
        intro: {
          1: "We are not just creating a project. We are forming an environment where everyone can find like-minded people, realize their ideas, and feel part of something bigger. In Commune, we value trust, mutual support, and real contribution to each other's lives.",
          2: "At the heart of Commune lies the idea of community. We offer tools that help us better understand each other and create our unique culture: a flexible voting system, a common language for more precise communication, our own calendar and even holidays. These are not mandatory rules, but opportunities that allow us to strengthen bonds and make our community truly united.",
          3: "Our goal is to create a self-sufficient and independent environment. Therefore, there is and will be no advertising in Commune. We believe that for stable development, the project should rely on the support of the participants themselves, not on external funding. All funds are directed to improving our common environment.",
          4: "We invite you on a joint journey where we take every step together.",
        },
        phases: {
          phase0: {
            title: "Phase 0: Foundation — We Are Here",
            status: "Completed",
            icon: "📍",
            description:
              "The solid foundation of our digital home has been laid. We have created key tools that already allow us to be together and interact in new ways.",
            features: [
              "Commune System: Create your small groups, unite with friends and loved ones, forming the core of our society.",
              "Reactor: A space for free exchange of opinions, where the value of an idea is more important than the loudness of the voice.",
              "Social Opinion System: A comprehensive and honest evaluation system. It helps the community effectively self-regulate, encouraging constructive contribution and healthy discussion.",
            ],
          },
          phase1: {
            title: "Phase 1: Opening the Gates — Welcome",
            status: "In Progress",
            icon: "🔥",
            description:
              "We have opened our doors for everyone who shares our values. At this stage, our main goal is community growth and debugging all mechanisms based on your feedback.",
            features: [
              "Free Access: All Commune functionality is available for free so you can explore everything and immerse yourself in our atmosphere.",
              "Core Community Formation: We are looking for like-minded people who will become the foundation of Commune and help determine its future path.",
              "Direct Communication and Open Dialogue: All communication about project development is conducted in our Telegram channels and chats. You can directly ask questions and participate in discussions.",
            ],
            yourRoleTitle: "Your Role Now:",
            yourRole:
              "Join the first explorers! Help us test the system and determine what the next step will be. Your voice at this stage is the most important.",
          },
          phase2: {
            title: "Phase 2: System Improvement — Becoming",
            status: "In Development",
            icon: "🛠",
            description:
              "We fully focus on quality and convenience, turning feedback from the first participants into real improvements. Our goal is to make Commune the most useful and comfortable environment.",
            features: [
              "Deep Feedback Work: Actively collecting your ideas and suggestions for improving the portal and its functionality.",
              "Development of New Capabilities: Launching the first internal services in test mode. For example, the 'Conflict Resolution' service, which will help fairly resolve disputes within the community.",
              "Improved Convenience: Improving the interface, speeding up the site, making navigation intuitively clear. Your convenience is our priority.",
            ],
          },
          phase3: {
            title: "Phase 3: Community Economy — Growth and Stability",
            status: "Planning",
            icon: "🌱",
            description:
              "To ensure independence and further development, we create a sustainable economic model that works for the benefit of all participants.",
            features: [
              "Section Subscriptions: Affordable subscriptions to individual parts of the portal will appear, allowing you to support what you use most often.",
              "Full Portal Access: A main subscription will appear, opening access to all capabilities. Its initial cost will be $10-15 per month. In the future, as the value and number of services grow, we plan to bring it to a target level of $50, to fully cover the growing needs of the community and obtain sufficient resources for further development.",
              "Free periods will be provided for first participants.",
              "Flexible and Fair Pricing: The cost of individual paid services will be periodically recalculated to honestly reflect the real costs of their support.",
              "Launch of Stability Fund and Management: Part of the funds will go to the stability fund, and voting on key development issues will be available for participants with full subscriptions.",
            ],
          },
          phase4: {
            title: "Phase 4: Material Embodiment — Building Our World",
            status: "Dream We Will Make Reality",
            icon: "🌳",
            description:
              "Our common capabilities begin to work in the real world, creating a comfortable and accessible environment for Commune participants.",
            features: [
              "Common Real Estate: Buying the first apartments and houses for rent to participants at below-market prices.",
              "Common Property: Forming a park of publicly available things: cars, professional tools, tourist equipment.",
              "Social Projects and Health Care: Improving the environment, organizing health activities and creating support programs for participants.",
            ],
          },
          phase5: {
            title: "Phase 5: New Horizons — Our Future",
            status: "Vision",
            icon: "✨",
            description:
              "When our system becomes fully stable and self-sufficient, we will be able to implement projects that today seem like fantasy.",
            features: [
              "Insurance System: Our own, maximally honest and profitable insurance system.",
              "Venture Fund: Investing in startups and projects of participants, helping them realize their dreams.",
              "Educational Programs: Creating a platform for knowledge exchange and learning.",
            ],
          },
        },
        conclusionTitle: "Join Us!",
        conclusion:
          "By joining Commune today, you are not just registering on a website. You are laying the first stone in the foundation of a world built on principles we all believe in.",
      },

      initiatives: {
        title: "Our Initiatives",
        description:
          "Innovative projects we're developing to improve communication and organization",
        readMore: "Read more",
        initiatives: {
          theLaw: {
            title: "The Law",
            description:
              "An adaptive system of principles aimed at building a rational, free, and just society based on reason and human dignity.",
          },
          rules: {
            title: "Rules",
            description:
              "Practical guidelines for our digital community that promote constructive interaction and a positive environment.",
          },
          newCalendar: {
            title: "New Calendar",
            description:
              "A revolutionary fixed calendar designed for the modern digital age with consistent structure and intuitive design.",
          },
          newEnglish: {
            title: "New English",
            description:
              "A systematic approach to reforming English spelling and pronunciation for improved consistency and clarity.",
          },
        },
      },
    },

    ru: {
      _page: {
        title: "Коммуна",
      },

      hero: {
        title: "Коммуна",
        subtitle: "Созидание через технологический прогресс",
      },

      roadmap: {
        title: "Дорожная карта",
        subtitle: "Строим наше общее будущее вместе",
        intro: {
          1: "Мы создаём не просто проект. Мы формируем среду, где каждый может найти единомышленников, реализовать свои идеи и почувствовать себя частью чего-то большего. В Коммуне мы ценим доверие, взаимную поддержку и реальный вклад в жизнь друг друга.",
          2: "В основе Коммуны лежит идея общности. Мы предлагаем инструменты, которые помогают нам лучше понимать друг друга и создавать свою уникальную культуру: гибкая система голосования, общий язык для более точного общения, свой календарь и даже праздники. Это не обязательные правила, а возможности, которые позволяют нам укреплять связи и делать наше сообщество по-настоящему сплочённым.",
          3: "Наша цель — создать самодостаточную и независимую среду. Поэтому в Коммуне нет и не будет рекламы. Мы верим, что для стабильного развития проект должен опираться на поддержку самих участников, а не на внешнее финансирование. Все средства направляются на улучшение нашей общей среды.",
          4: "Приглашаем вас в совместное путешествие, где каждый шаг мы делаем вместе.",
        },
        phases: {
          phase0: {
            title: "Этап 0: Фундамент — Мы здесь",
            status: "Завершён",
            icon: "📍",
            description:
              "Прочный фундамент нашего цифрового дома заложен. Мы создали ключевые инструменты, которые уже сейчас позволяют нам быть вместе и взаимодействовать по-новому.",
            features: [
              "Система коммун: создавайте свои небольшие группы, объединяйтесь с друзьями и близкими, формируя ядро нашего общества.",
              "Реактор: пространство для свободного обмена мнениями, где ценность идеи важнее громкости голоса.",
              "Система общественного мнения: комплексная и честная система оценки. Она помогает сообществу эффективно саморегулироваться, поощряя конструктивный вклад и здоровую дискуссию.",
            ],
          },
          phase1: {
            title: "Этап 1: Открытие Врат — Добро пожаловать",
            status: "В процессе",
            icon: "🔥",
            description:
              "Мы открыли двери для всех, кто разделяет наши ценности. На этом этапе наша главная цель — рост сообщества и отладка всех механизмов на основе ваших отзывов.",
            features: [
              "Свободный доступ: весь функционал Коммуны доступен бесплатно, чтобы вы могли всё изучить и погрузиться в нашу атмосферу.",
              "Формирование ядра сообщества: мы ищем единомышленников, которые станут основой Коммуны и помогут определить её дальнейший путь.",
              "Прямая связь и открытый диалог: всё общение о развитии проекта ведётся в наших каналах и чатах в Телеграме. Вы можете напрямую задавать вопросы и участвовать в обсуждениях.",
            ],
            yourRoleTitle: "Ваша роль сейчас:",
            yourRole:
              "Присоединяйтесь к первым исследователям! Помогите нам протестировать систему и определить, каким будет следующий шаг. Ваш голос на этом этапе — самый важный.",
          },
          phase2: {
            title: "Этап 2: Совершенствование Системы — Становление",
            status: "В разработке",
            icon: "🛠",
            description:
              "Мы полностью концентрируемся на качестве и удобстве, превращая отзывы первых участников в реальные улучшения. Наша цель — сделать Коммуну максимально полезной и комфортной средой.",
            features: [
              "Глубокая работа с обратной связью: активно собираем ваши идеи и предложения по улучшению портала и его функционала.",
              'Развитие новых возможностей: в тестовом режиме запускаем первые внутренние сервисы. Например, сервис "Решение конфликтов", который поможет справедливо урегулировать споры внутри сообщества.',
              "Повышение удобства: улучшаем интерфейс, ускоряем работу сайта, делаем навигацию интуитивно понятной. Ваше удобство — наш приоритет.",
            ],
          },
          phase3: {
            title: "Этап 3: Экономика Сообщества — Рост и стабильность",
            status: "Планирование",
            icon: "🌱",
            description:
              "Чтобы обеспечить независимость и дальнейшее развитие, мы создаём устойчивую экономическую модель, которая работает на благо всех участников.",
            features: [
              "Подписки на разделы: появятся недорогие подписки на отдельные части портала, позволяя поддерживать то, чем вы пользуетесь чаще всего.",
              "Полный доступ к порталу: появится основная подписка, открывающая доступ ко всем возможностям. Её начальная стоимость составит $10-15 в месяц. В будущем, по мере роста ценности и количества сервисов, мы планируем довести её до целевого уровня в $50, чтобы полностью покрывать растущие потребности сообщества и получить достаточно ресурсов для дальнейшего развития.",
              "Для первых участников будут предусмотрены бесплатные периоды.",
              "Гибкая и честная цена: стоимость отдельных платных сервисов будет периодически пересчитываться, чтобы честно отражать реальные затраты на их поддержку.",
              "Запуск стабфонда и управления: часть средств пойдёт в стабфонд, а для участников с полной подпиской будет доступно голосование по ключевым вопросам развития.",
            ],
          },
          phase4: {
            title: "Этап 4: Материальное Воплощение — Строим наш мир",
            status: "Мечта, которую мы сделаем реальностью",
            icon: "🌳",
            description:
              "Наши общие возможности начинают работать в реальном мире, создавая комфортную и доступную среду для участников Коммуны.",
            features: [
              "Общая недвижимость: покупаем первые квартиры и дома для сдачи в аренду участникам по ценам ниже рыночных.",
              "Общее имущество: формируем парк общедоступных вещей: автомобили, профессиональные инструменты, туристическое снаряжение.",
              "Социальные проекты и забота о здоровье: благоустраиваем среду, организуем оздоровительные мероприятия и создаём программы поддержки для участников.",
            ],
          },
          phase5: {
            title: "Этап 5: Новые Горизонты — Наше будущее",
            status: "Видение",
            icon: "✨",
            description:
              "Когда наша система станет полностью стабильной и самодостаточной, мы сможем реализовать проекты, которые сегодня кажутся фантастикой.",
            features: [
              "Система страхования: наша собственная, максимально честная и выгодная система страхования.",
              "Венчурный фонд: инвестируем в стартапы и проекты участников, помогая им реализовать свои мечты.",
              "Образовательные программы: создаём платформу для обмена знаниями и обучения.",
            ],
          },
        },
        conclusionTitle: "Присоединяйтесь к нам!",
        conclusion:
          "Присоединяясь к Коммуне сегодня, вы не просто регистрируетесь на сайте. Вы закладываете первый камень в основание мира, построенного на принципах, в которые мы все верим.",
      },

      initiatives: {
        title: "Наши инициативы",
        description:
          "Инновационные проекты, которые мы разрабатываем для улучшения коммуникаций и организации",
        readMore: "Подробнее",
        initiatives: {
          theLaw: {
            title: "Право",
            description:
              "Адаптивная система принципов, направленных на создание рационального, свободного и справедливого общества на основе разума и человеческой достоинства.",
          },
          rules: {
            title: "Правила",
            description:
              "Практические рекомендации для нашей цифровой общины, которые способствуют конструктивному взаимодействию и положительной среде.",
          },
          newCalendar: {
            title: "Новый календарь",
            description:
              "Новый календарь, разработанный для современной цифровой эпохи с последовательной структурой и интуитивным дизайном.",
          },
          newEnglish: {
            title: "Новый английский",
            description:
              "Систематический подход к реформированию английской орфографии и произношения для улучшения согласованности и ясности.",
          },
        },
      },
    },
  };

  const { data } = $props();
  const { locale, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<section
  class="position-relative vh-100 d-flex align-items-center justify-content-center overflow-hidden"
>
  <div
    class="position-absolute top-0 start-0 w-100 h-100 bg-primary-gradient opacity-90 z-index-1"
  ></div>

  <div class={`position-absolute top-0 start-0 w-100 h-100 z-index-0 hero-background`}></div>

  <div class="z-index-2 text-center hero-content">
    <div class="d-inline-block">
      <h1
        class="display-1 fw-bold text-white mb-3 lh-sm"
        style:text-shadow="2px 2px 4px rgba(0, 0, 0, 0.8)"
      >
        {t.hero.title}
      </h1>

      <h3 class="fs-2 text-light mb-5" style:text-shadow="1px 1px 3px rgba(0, 0, 0, 0.8)">
        {t.hero.subtitle}
      </h3>
    </div>
  </div>
</section>

<section class="py-5 py-lg-6 bg-white">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10 text-center mb-5">
        <h2 class="display-4 fw-bold mb-4">{t.roadmap.title}</h2>
        <h3 class="h4 text-primary mb-4">{t.roadmap.subtitle}</h3>
      </div>
    </div>

    <!-- Introduction -->
    <div class="row justify-content-center mb-5">
      <div class="col-lg-8">
        <div class="card border-0 shadow-sm rounded-4 bg-light">
          <div class="card-body p-5">
            <div class="row g-4">
              <div class="col-md-6">
                <p class="mb-3">{t.roadmap.intro[1]}</p>
                <p class="mb-0">{t.roadmap.intro[2]}</p>
              </div>
              <div class="col-md-6">
                <p class="mb-3">{t.roadmap.intro[3]}</p>
                <p class="mb-0 fw-semibold text-primary">{t.roadmap.intro[4]}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Roadmap Phases -->
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="roadmap-timeline">
          <!-- Phase 0 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="roadmap-phase-left col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon completed">
                  <span class="fs-1">{t.roadmap.phases.phase0.icon}</span>
                </div>
                <div class="roadmap-status completed mt-2">
                  <small class="fw-bold text-success">{t.roadmap.phases.phase0.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div class="card border-0 shadow-sm rounded-4 border-start border-success border-4">
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-success mb-3">{t.roadmap.phases.phase0.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase0.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase0.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-check-circle-fill text-success me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 1 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon current">
                  <span class="fs-1">{t.roadmap.phases.phase1.icon}</span>
                </div>
                <div class="roadmap-status current mt-2">
                  <small class="fw-bold text-primary">{t.roadmap.phases.phase1.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div class="card border-0 shadow-sm rounded-4 border-start border-primary border-4">
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-primary mb-3">{t.roadmap.phases.phase1.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase1.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase1.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-arrow-right-circle text-primary me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                    <div class="alert alert-primary border-0 mb-0">
                      <strong>{t.roadmap.phases.phase1.yourRoleTitle}</strong>
                      {t.roadmap.phases.phase1.yourRole}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 2 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon future">
                  <span class="fs-1">{t.roadmap.phases.phase2.icon}</span>
                </div>
                <div class="roadmap-status future mt-2">
                  <small class="fw-bold text-warning">{t.roadmap.phases.phase2.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div class="card border-0 shadow-sm rounded-4 border-start border-warning border-4">
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-warning mb-3">{t.roadmap.phases.phase2.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase2.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase2.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-gear text-warning me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 3 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon future">
                  <span class="fs-1">{t.roadmap.phases.phase3.icon}</span>
                </div>
                <div class="roadmap-status future mt-2">
                  <small class="fw-bold text-info">{t.roadmap.phases.phase3.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div class="card border-0 shadow-sm rounded-4 border-start border-info border-4">
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-info mb-3">{t.roadmap.phases.phase3.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase3.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase3.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-currency-dollar text-info me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 4 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon future">
                  <span class="fs-1">{t.roadmap.phases.phase4.icon}</span>
                </div>
                <div class="roadmap-status future mt-2">
                  <small class="fw-bold text-secondary">{t.roadmap.phases.phase4.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div
                  class="card border-0 shadow-sm rounded-4 border-start border-secondary border-4"
                >
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-secondary mb-3">{t.roadmap.phases.phase4.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase4.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase4.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-house text-secondary me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 5 -->
          <div class="roadmap-phase mb-5">
            <div class="row align-items-center">
              <div class="col-lg-2 text-center mb-3 mb-lg-0">
                <div class="roadmap-icon future">
                  <span class="fs-1">{t.roadmap.phases.phase5.icon}</span>
                </div>
                <div class="roadmap-status future mt-2">
                  <small class="fw-bold text-dark">{t.roadmap.phases.phase5.status}</small>
                </div>
              </div>
              <div class="col-lg-10">
                <div class="card border-0 shadow-sm rounded-4 border-start border-dark border-4">
                  <div class="card-body p-4">
                    <h4 class="fw-bold text-dark mb-3">{t.roadmap.phases.phase5.title}</h4>
                    <p class="mb-3">{t.roadmap.phases.phase5.description}</p>
                    <ul class="list-unstyled mb-0">
                      {#each t.roadmap.phases.phase5.features as feature}
                        <li class="mb-2">
                          <i class="bi bi-star text-dark me-2"></i>
                          {feature}
                        </li>
                      {/each}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conclusion -->
    <div class="row justify-content-center mt-5">
      <div class="col-lg-8 text-center">
        <div class="card border-0 shadow-lg rounded-4 bg-primary text-white">
          <div class="card-body p-5">
            <h4 class="fw-bold mb-3">{t.roadmap.conclusionTitle}</h4>
            <p class="mb-0 fs-5">{t.roadmap.conclusion}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{#snippet initiative(title: string, description: string, link: string, icon: string, color: string)}
  <div class="col-md-6 col-lg-6">
    <a href={toLocaleHref(link)} class="text-decoration-none">
      <div
        class={`card h-100 border-0 shadow-sm hover-shadow rounded-4 overflow-hidden bg-${color}-subtle`}
      >
        <div class="card-body p-4">
          <div class="d-flex align-items-center mb-4">
            <div class={`bg-${color}-subtle rounded-circle p-3 me-3`}>
              <span class="fs-2">{icon}</span>
            </div>
            <h3 class={`h4 fw-bold mb-0 text-${color}-emphasis`}>
              {title}
            </h3>
          </div>
          <p class="mb-0">{description}</p>
        </div>
        <div class={`card-footer bg-${color}-subtle border-0 py-3`}>
          <div class="d-flex align-items-center">
            <span class={`text-${color}-emphasis fw-semibold`}>
              {t.initiatives.readMore}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-arrow-right ms-2"
                viewBox="0 0 16 16"
              >
                <path
                  fill-rule="evenodd"
                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </a>
  </div>
{/snippet}

<section class="py-5 py-lg-6 bg-light" id="initiatives">
  <div class="row justify-content-center mb-5">
    <div class="col-lg-8 text-center">
      <h2 class="display-5 fw-bold mb-4">{t.initiatives.title}</h2>
      <p class="text-secondary fs-5">{t.initiatives.description}</p>
    </div>
  </div>

  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="row g-4">
        {@render initiative(
          t.initiatives.initiatives.theLaw.title,
          t.initiatives.initiatives.theLaw.description,
          "/the-law",
          "⚖️",
          "danger",
        )}

        {@render initiative(
          t.initiatives.initiatives.rules.title,
          t.initiatives.initiatives.rules.description,
          "/rules",
          "📋",
          "info",
        )}

        {@render initiative(
          t.initiatives.initiatives.newCalendar.title,
          t.initiatives.initiatives.newCalendar.description,
          "/new-calendar",
          "📅",
          "primary",
        )}

        {@render initiative(
          t.initiatives.initiatives.newEnglish.title,
          t.initiatives.initiatives.newEnglish.description,
          "/new-english",
          "🔤",
          "success",
        )}
      </div>
    </div>
  </div>
</section>

<style>
  .page {
    --gray-rgb: 0, 0, 0;
    --gray-alpha-200: rgba(var(--gray-rgb), 0.08);
    --gray-alpha-100: rgba(var(--gray-rgb), 0.05);

    --button-primary-hover: #383838;
    --button-secondary-hover: #f2f2f2;

    display: grid;
    grid-template-rows: 20px 1fr 20px;
    align-items: center;
    justify-items: center;
    min-height: 100svh;
    padding: 80px;
    gap: 64px;
    font-family: var(--font-geist-sans);
  }

  @media (prefers-color-scheme: dark) {
    .page {
      --gray-rgb: 255, 255, 255;
      --gray-alpha-200: rgba(var(--gray-rgb), 0.145);
      --gray-alpha-100: rgba(var(--gray-rgb), 0.06);

      --button-primary-hover: #ccc;
      --button-secondary-hover: #1a1a1a;
    }
  }

  .main {
    display: flex;
    flex-direction: column;
    gap: 32px;
    grid-row-start: 2;
  }

  .ctas {
    display: flex;
    gap: 16px;
  }

  .footer {
    grid-row-start: 3;
    display: flex;
    gap: 24px;
  }

  @media (max-width: 600px) {
    .page {
      padding: 32px;
      padding-bottom: 80px;
    }

    .main {
      align-items: center;
    }

    .ctas {
      flex-direction: column;
    }

    .footer {
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
    }
  }

  @media (prefers-color-scheme: dark) {
    .logo {
      filter: invert();
    }
  }

  .hero-background {
    background-image: url("/images/home-page-main-image.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center bottom;
  }

  .hero-content {
    position: relative;
  }

  /* Roadmap Styles */
  .roadmap-timeline {
    position: relative;
  }

  .roadmap-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
  }

  .roadmap-icon.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  }

  .roadmap-icon.current {
    background: linear-gradient(135deg, #007bff, #6610f2);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    animation: pulse 2s infinite;
  }

  .roadmap-icon.future {
    background: linear-gradient(135deg, #6c757d, #adb5bd);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.5);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }
  }

  .roadmap-phase {
    position: relative;
  }

  /* .roadmap-phase:not(:last-child)::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 80px;
    width: 2px;
    height: calc(100% - 80px);
    background: linear-gradient(to bottom, #dee2e6, #f8f9fa);
    transform: translateX(-50%);
    z-index: 0;
  } */

  /* @media (min-width: 992px) {
    .roadmap-phase:not(:last-child)::after {
      left: 120px;
    }
  } */

  .roadmap-phase .card {
    transition: all 0.3s ease;
  }

  .roadmap-phase .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  }

  @media (max-width: 767.98px) {
    .hero-background {
      background-image: url("/images/home-page-main-image-mobile.png");
    }

    .hero-content {
      position: absolute;
      top: 35%;
    }

    .roadmap-icon {
      width: 60px;
      height: 60px;
    }

    /* .roadmap-phase:not(:last-child)::after {
      left: 50%;
    } */
  }
</style>
