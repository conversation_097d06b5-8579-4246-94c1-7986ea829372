"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingController = void 0;
const common_1 = require("@nestjs/common");
const acrpc_1 = require("../acrpc");
const rating_service_1 = require("./rating.service");
let RatingController = class RatingController {
    constructor(ratingService) {
        this.ratingService = ratingService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            rating: {
                karma: {
                    list: {
                        get: async (input) => {
                            const points = await this.ratingService.getKarmaPoints(input);
                            return points.map((p) => ({
                                id: p.id,
                                author: {
                                    ...p.sourceUser,
                                    image: p.sourceUser.image?.url ?? null,
                                },
                                quantity: p.quantity,
                                comment: p.comment,
                            }));
                        },
                    },
                    post: (input, metadata) => this.ratingService.spendKarmaPoint(input, metadata.user),
                },
                feedback: {
                    list: {
                        get: async (input) => {
                            const feedbacks = await this.ratingService.getUserFeedbacks(input);
                            return feedbacks.map((f) => ({
                                id: f.id,
                                author: f.isAnonymous
                                    ? null
                                    : {
                                        ...f.sourceUser,
                                        image: f.sourceUser.image?.url ?? null,
                                    },
                                isAnonymous: f.isAnonymous,
                                value: f.value,
                                text: f.text,
                            }));
                        },
                    },
                    post: (input, metadata) => this.ratingService.createUserFeedback(input, metadata.user),
                },
                summary: {
                    get: (input) => this.ratingService.getUserSummary(input),
                },
            },
        });
    }
};
exports.RatingController = RatingController;
exports.RatingController = RatingController = __decorate([
    (0, common_1.Controller)("rating"),
    __metadata("design:paramtypes", [rating_service_1.RatingService])
], RatingController);
//# sourceMappingURL=rating.controller.js.map