<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";

  const i18n = {
    en: {
      _page: {
        title: "People — Commune",
      },
      users: "People",
      loading: "Loading...",
      noUsersFound: "People not found",
      errorFetchingUsers: "Failed to fetch people",
      errorOccurred: "An error occurred while fetching people",
      loadingMore: "Loading more people...",
      noImage: "No image",
      userImageAlt: "Avatar",
      role: {
        admin: "Admin",
      },
    },
    ru: {
      _page: {
        title: "Люди — Коммуна",
      },
      users: "Люди",
      loading: "Загрузка...",
      noUsersFound: "Люди не найдены",
      errorFetchingUsers: "Не удалось загрузить людей",
      errorOccurred: "Произошла ошибка при загрузке людей",
      loadingMore: "Загружаем больше людей...",
      noImage: "Нет изображения",
      userImageAlt: "Аватарка",
      role: {
        admin: "Админ",
      },
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let users = $state(data.users);
  let error = $state<string | null>(null);

  let isLoadingMore = $state(false);

  let currentPage = $state(1);
  let isHasMoreUsers = $state(data.isHasMoreUsers);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Function to load more users
  async function loadMoreUsers() {
    if (isLoadingMore || !isHasMoreUsers) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newUsers = await api.user.list.get({
        pagination: { page: nextPage },
      });

      users = [...users, ...newUsers];
      currentPage = nextPage;

      isHasMoreUsers = newUsers.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreUsers && !isLoadingMore) {
            loadMoreUsers();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <h1>{t.users}</h1>
  </div>

  {#if users.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noUsersFound}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4">
      {#each users as user (user.id)}
        <div class="col">
          <div class="card h-100 shadow-sm hover-card">
            <a href={toLocaleHref(`/users/${user.id}`)} class="text-decoration-none text-black">
              {#if user.image}
                <div
                  style="height: 140px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"
                >
                  <img
                    src={`/images/${user.image}`}
                    alt={`${t.userImageAlt}`}
                    style="width: 100%; height: 100%; object-fit: cover;"
                  />
                </div>
              {:else}
                <div
                  class="bg-light text-center d-flex align-items-center justify-content-center"
                  style="height: 140px;"
                >
                  <span class="text-muted">{t.noImage}</span>
                </div>
              {/if}
              <div class="card-body d-flex flex-column">
                <h5 class="card-title fs-5 text-truncate">
                  {getAppropriateLocalization(user.name)}
                </h5>

                <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">
                  {getAppropriateLocalization(user.description) || ""}
                </p>

                <div class="mt-auto">
                  <div class="small text-muted">
                    {#if user.role === "admin"}
                      <span class="badge bg-danger">
                        {t.role.admin}
                      </span>
                    {/if}
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreUsers}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<style>
  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }
</style>
