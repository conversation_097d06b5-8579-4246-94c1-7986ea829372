import { Injectable } from "@nestjs/common";
import { CommuneMember, CommuneMemberType } from "@prisma/client";

@Injectable()
export class CommuneCore {
    isHeadMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.deletedAt === null &&
                member.isHead &&
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }

    isMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.deletedAt === null &&
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }
}
