import { Common, Commune } from "@commune/api";
import { CommuneMember } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";
export declare class CommuneMemberService {
    private readonly prisma;
    private readonly communeCore;
    constructor(prisma: PrismaService, communeCore: CommuneCore);
    hydrateMembers(members: CommuneMember[]): Promise<Commune.GetCommuneMembersOutput>;
    getCommuneMembers(input: Commune.GetCommuneMembersInput): Promise<{
        id: string;
        createdAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        deletedAt: Date | null;
        actorType: "user";
        actorId: string;
    }[]>;
    createCommuneMember(input: Commune.CreateCommuneMemberInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        actorType: import("@prisma/client").$Enums.CommuneMemberType;
        actorId: string;
        communeId: string;
        isHead: boolean;
    }>;
    deleteCommuneMember(input: Common.ObjectWithId, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        actorType: import("@prisma/client").$Enums.CommuneMemberType;
        actorId: string;
        communeId: string;
        isHead: boolean;
    }>;
}
