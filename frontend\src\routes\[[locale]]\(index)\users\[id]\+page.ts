import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();

  const [
    [user],
    note,
    summary,
  ] = await Promise.all([
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.user.note.get({ userId: params.id }, { fetch, ctx: { url } }),
    api.rating.summary.get({ userId: params.id }, { fetch, ctx: { url } }),
  ]);

  if (!user) {
    throw error(404, "User not found");
  }

  return {
    user,
    userNote: note.text,
    ratingSummary: summary,
  };
};
