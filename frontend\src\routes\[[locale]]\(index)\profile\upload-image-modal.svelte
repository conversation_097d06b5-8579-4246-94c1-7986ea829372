<script lang="ts">
  import type { Common } from "@commune/api";

  import { Consts } from "@commune/api";
  import { fetchWithAuth } from "$lib";
  import { Modal, EntityImageInput } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    userId: string;
    onImageUploaded: () => void;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      uploadImage: "Upload Profile Image",
      upload: "Upload",
      cancel: "Cancel",
      uploading: "Uploading...",
      imageUploadedSuccessfully: "Image uploaded successfully!",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Failed to upload image",
      errorOccurred: "An error occurred while uploading the image",
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
    },

    ru: {
      uploadImage: "Загрузить изображение профиля",
      upload: "Загрузить",
      cancel: "Отменить",
      uploading: "Загрузка...",
      imageUploadedSuccessfully: "Изображение загружено успешно!",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Не удалось загрузить изображение",
      errorOccurred: "Произошла ошибка при загрузке изображения",
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
    },
  };

  const { locale, show, onHide, userId, onImageUploaded }: Props = $props();

  const t = $derived(i18n[locale]);

  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);
  let entityImageInput: any = $state();

  const handleImageSave = (file: File) => {
    // File is ready for upload, we'll handle it in handleSubmit
  };

  const handleSubmit = async () => {
    const fileToUpload = entityImageInput?.getCurrentFile();

    if (!fileToUpload) {
      error = t.pleaseSelectImage;
      return;
    }

    isSubmitting = true;
    error = "";

    try {
      const formData = new FormData();
      formData.append("image", fileToUpload);

      const response = await fetchWithAuth(`/api/user/${userId}/image`, {
        method: "PUT",
        body: formData,
        // Don't set Content-Type header, it will be set automatically with the boundary
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }

      submitSuccess = true;
      onImageUploaded();

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    entityImageInput?.reset();
    error = "";
    submitSuccess = false;
    onHide();
  };
</script>

<Modal
  {show}
  title={t.uploadImage}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.uploading : t.upload}
  cancelText={t.cancel}
  submitDisabled={!entityImageInput?.hasFile() || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
  size="lg"
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.imageUploadedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <EntityImageInput
    bind:this={entityImageInput}
    {locale}
    onSave={handleImageSave}
    disabled={isSubmitting}
    label={t.pleaseSelectImage}
    helpText={t.uploadImageMaxSize}
    editButtonText={t.editImage}
    selectDifferentButtonText={t.selectDifferentImage}
  />
</Modal>
