enum UserRatingEntityType {
    post
    comment
}

// represents any rating changes
model UserRating {
    @@map("user_ratings")

    id String @id @default(nanoid())

    sourceUserId String @map("source_user_id")
    sourceUser   User   @relation("user_rating_source_user_user", fields: [sourceUserId], references: [id])

    targetUserId String @map("target_user_id")
    targetUser   User   @relation("user_rating_target_user_user", fields: [targetUserId], references: [id])

    entityType UserRatingEntityType @map("entity_type")
    entityId   String               @map("entity_id")

    value Int @db.SmallInt

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())

    @@unique([sourceUserId, targetUserId, entityType, entityId])
}

// quantity of karma points available to user
model UserKarmaSpendablePoint {
    @@map("user_karma_spendable_points")

    id String @id @default(nanoid())

    userId String @map("user_id")

    points Int @db.SmallInt @default(0)

    @@unique([userId])
}

// spent karma points, each one is karma change action
model UserKarmaGivenPoint {
    @@map("user_karma_given_points")

    id String @id @default(nanoid())

    sourceUserId String @map("source_user_id")
    sourceUser   User   @relation("user_karma_given_point_source_user_user", fields: [sourceUserId], references: [id])

    targetUserId String @map("target_user_id")
    targetUser   User   @relation("user_karma_given_point_target_user_user", fields: [targetUserId], references: [id])

    comment Localization[] @relation("user_karma_given_point_comment")

    quantity Int

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt
}

// detailed feedback for user behavior
model UserFeedback {
    @@map("user_feedbacks")

    id String @id @default(nanoid())

    sourceUserId String @map("source_user_id")
    sourceUser   User   @relation("user_feedback_source_user_user", fields: [sourceUserId], references: [id])

    targetUserId String @map("target_user_id")
    targetUser   User   @relation("user_feedback_target_user_user", fields: [targetUserId], references: [id])

    value Int @db.SmallInt

    isAnonymous Boolean @map("is_anonymous") @default(false)

    text Localization[] @relation("user_feedback_text")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
}
