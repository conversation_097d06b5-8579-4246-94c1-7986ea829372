import { Common } from "@commune/api";
import { OnModuleInit } from "@nestjs/common";
import { Transporter } from "nodemailer";
import { ConfigService } from "src/config/config.service";
type SendEmailDto = {
    transporter: Transporter;
    from: string;
    to: string[];
    subject: string;
    text?: string;
    html?: string;
};
export declare class EmailService implements OnModuleInit {
    private readonly configService;
    private otpTransporter;
    private inviteTransporter;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    joinAddress(sender: string, domain: string): string;
    send(dto: SendEmailDto): Promise<boolean>;
    sendOtp(dto: {
        to: string;
        otp: string;
    }): Promise<boolean>;
    sendInvite(dto: {
        to: string;
        name: string | null;
        locale: Common.LocalizationLocale;
    }): Promise<boolean>;
}
export {};
