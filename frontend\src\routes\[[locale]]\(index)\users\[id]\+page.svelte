<script lang="ts">
  import { getUserRateColor } from "$lib";
  import { getClient } from "$lib/acrpc";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      userNotFound: "Person not found",
      userDetails: "Person Details",
      joinedOn: "Joined on",
      dateFormatLocale: "en-US",
      userNote: "Personal Note",
      userNotePlaceholder: "Write your personal note about this person...",
      saved: "Saved...",
      rating: "Rating",
      karma: "Karma",
      rate: "Rate",
      noImage: "No image available",
      userImageAlt: "Avatar",
      socialOpinion: "Social Opinion",
      alignmentSystem: "Alignment System",
      alignmentTypes: {
        lawfulGood: "Lawful Good",
        neutralGood: "Neutral Good",
        chaoticGood: "Chaotic Good",
        lawfulNeutral: "Lawful Neutral",
        trueNeutral: "True Neutral",
        chaoticNeutral: "Chaotic Neutral",
        lawfulEvil: "Lawful Evil",
        neutralEvil: "Neutral Evil",
        chaoticEvil: "Chaotic Evil",
      },
      noAlignment: "No alignment set",
      alignmentSystemLink: "https://en.wikipedia.org/wiki/Alignment_(Dungeons_%26_Dragons)",
      alignmentSystemLinkLabel: "Alignment System in D&D (Wikipedia)",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      userNotFound: "Человек не найден",
      userDetails: "Информация о человеке",
      joinedOn: "Дата регистрации",
      dateFormatLocale: "ru-RU",
      userNote: "Личная заметка",
      userNotePlaceholder: "Напишите свою личную заметку об этом человеке...",
      saved: "Сохранено...",
      rating: "Рейтинг",
      karma: "Карма",
      rate: "Оценка",
      noImage: "Нет доступных изображений",
      userImageAlt: "Аватарка",
      socialOpinion: "Общественное мнение",
      alignmentSystem: "Мировоззрение",
      alignmentTypes: {
        lawfulGood: "Lawful Good",
        neutralGood: "Neutral Good",
        chaoticGood: "Chaotic Good",
        lawfulNeutral: "Lawful Neutral",
        trueNeutral: "True Neutral",
        chaoticNeutral: "Chaotic Neutral",
        lawfulEvil: "Lawful Evil",
        neutralEvil: "Neutral Evil",
        chaoticEvil: "Chaotic Evil",
      },
      noAlignment: "Не указано",
      alignmentSystemLink: "https://ru.wikipedia.org/wiki/Мировоззрение_в_Dungeons_%26_Dragons",
      alignmentSystemLinkLabel: "Мировоззрение в D&D (Википедия)",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { user, locale, getAppropriateLocalization, ratingSummary, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);

  // User note state
  let userNote = $state(data.userNote);
  let saveTimeout: ReturnType<typeof setTimeout> | null = $state(null);
  let showSaved = $state(false);

  // Initialize currentNote when userNote is available
  $effect(() => {
    if (userNote !== undefined) {
      userNote = userNote || "";
    }
  });

  const saveUserNote = async () => {
    await api.user.note.put({
      userId: user.id,
      text: userNote?.trim() || null,
    });

    showSaved = true;

    setTimeout(() => {
      showSaved = false;
    }, 2000);
  };

  // Debounced save function
  const debouncedSave = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    saveTimeout = setTimeout(() => {
      saveUserNote();
    }, 3000);
  };

  // Handle note input changes
  const handleNoteInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    userNote = target.value;
    debouncedSave();
  };

  // Handle note blur (unfocus)
  const handleNoteBlur = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      saveTimeout = null;
    }
    saveUserNote();
  };

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));
  const userDescription = $derived(getAppropriateLocalization(user.description));

  const joinDate = $derived(user ? new Date(user.createdAt) : new Date());
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Get badge class based on user role
  const getBadgeClass = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };

  const getAlignmentImagePath = (alignment: string) => {
    // Convert camelCase to kebab-case for file names
    const fileName = alignment.replace(/([A-Z])/g, "-$1").toLowerCase();
    return `/images/alignment-system/kung-fu-panda/${fileName}.png`;
  };

  // let mockRate = $state(0);
  // let direction = true;

  // $inspect(mockRate);

  // $effect(() => {
  //   const interval = setInterval(() => {
  //     if (direction) mockRate++;
  //     else mockRate--;

  //     if (mockRate === 0 || mockRate === 10) direction = !direction;
  //   }, 500);

  //   return () => clearInterval(interval);
  // });

  // const mockColor = $derived(getRateColor(mockRate));
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  <div class="row">
    <div class="col-lg-8">
      {#if user.image}
        <div
          style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;"
        >
          <img
            src={`/images/${user.image}`}
            alt={`${t.userImageAlt}`}
            style="width: 100%; height: 100%; object-fit: contain;"
          />
        </div>
      {:else}
        <div
          class="bg-light text-center rounded mb-4 d-flex align-items-center justify-content-center"
          style="height: 300px;"
        >
          <span class="text-muted">{t.noImage}</span>
        </div>
      {/if}

      <!-- User Information -->
      <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h2 class="mb-0">{userName}</h2>
          <!-- <span class={`badge ${getBadgeClass(user.role)}`}>
            {user.role}
          </span> -->
        </div>
        <p class="lead text-muted">{userDescription || ""}</p>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <h5 class="card-title">{t.userDetails}</h5>
          <hr />
          <div class="d-flex align-items-center">
            <i class="bi bi-calendar-date me-2 text-primary"></i>
            <span>{t.joinedOn} {formattedDate}</span>
          </div>
        </div>
      </div>

      <!-- Alignment System Card -->
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <h5 class="card-title">
            {t.alignmentSystem}
            <a href={t.alignmentSystemLink} target="_blank" aria-label={t.alignmentSystemLinkLabel}>
              <i class="bi bi-question-circle"></i>
            </a>
          </h5>
          <hr />
          <div class="d-flex align-items-center">
            <div class="me-3">
              {#if user.alignmentSystemType}
                <img
                  src={getAlignmentImagePath(user.alignmentSystemType)}
                  alt={t.alignmentTypes[user.alignmentSystemType]}
                  style="width: 170px; height: 110px;"
                />
              {/if}
            </div>
            <div>
              <h6 class="mb-0">
                {user.alignmentSystemType
                  ? t.alignmentTypes[user.alignmentSystemType]
                  : t.noAlignment}
              </h6>
            </div>
          </div>
        </div>
      </div>

      <!-- Rating Summary Card -->
      {#if ratingSummary}
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.socialOpinion}</h5>
            <hr />
            <div class="row g-3">
              <!-- Rating Block -->
              <div class="col-4">
                <div
                  class="rating-block border rounded p-3 text-center"
                  style="border-color: #fd7e14 !important;"
                >
                  <div class="rating-label text-muted small mb-1">{t.rating}</div>
                  <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;">
                    {ratingSummary.rating}
                  </div>
                </div>
              </div>

              <!-- Karma Block -->
              <div class="col-4">
                <a
                  href={toLocaleHref(`/users/${user.id}/karma`)}
                  class="karma-block border rounded p-3 text-center d-block text-decoration-none"
                  style="border-color: #d63384 !important;"
                >
                  <div class="karma-label text-muted small mb-1">{t.karma}</div>
                  <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;">
                    {ratingSummary.karma}
                  </div>
                </a>
              </div>

              <!-- Rate Block -->
              <div class="col-4">
                <a
                  href={toLocaleHref(`/users/${user.id}/feedback`)}
                  class="rate-block border rounded p-3 text-center d-block text-decoration-none"
                  style="border-color: #6c757d !important;"
                >
                  <div class="rate-label text-muted small mb-1">{t.rate}</div>
                  <div
                    class="rate-value fw-bold {ratingSummary.rate === null ? 'text-muted' : ''}"
                    style="font-size: 1.5rem; {ratingSummary.rate !== null
                      ? `color: ${getUserRateColor(ratingSummary.rate)};`
                      : ''}"
                  >
                    {ratingSummary.rate !== null ? ratingSummary.rate.toFixed(1) : "N/A"}
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- User Note Card -->
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <h5 class="card-title">{t.userNote}</h5>
          <hr />
          <div class="mb-2">
            <textarea
              class="form-control"
              rows="4"
              placeholder={t.userNotePlaceholder}
              value={userNote}
              oninput={handleNoteInput}
              onblur={handleNoteBlur}
            ></textarea>
          </div>
          {#if showSaved}
            <small class="text-muted">{t.saved}</small>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .rating-block,
  .karma-block,
  .rate-block {
    transition: all 0.2s ease-in-out;
  }

  .rating-block:hover,
  .karma-block:hover,
  .rate-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
</style>
