"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneService = void 0;
const client_1 = require("@prisma/client");
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const minio_service_1 = require("../minio/minio.service");
const commune_core_1 = require("./commune.core");
let CommuneService = class CommuneService {
    constructor(prisma, minioService, communeCore) {
        this.prisma = prisma;
        this.minioService = minioService;
        this.communeCore = communeCore;
    }
    async getCommuneIds() {
        return this.prisma.commune
            .findMany({
            where: {
                deletedAt: null,
            },
            select: {
                id: true,
            },
        })
            .then((communes) => communes.map((commune) => commune.id));
    }
    async transferHeadStatus(input, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.communeId,
                deletedAt: user.isAdmin ? undefined : null,
            },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
        }
        if (!this.communeCore.isMember(commune, input.newHeadUserId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("new_head_member_must_be_member"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.updateMany({
                where: {
                    communeId: input.communeId,
                },
                data: {
                    isHead: false,
                },
            });
            await trx.communeMember.updateMany({
                where: {
                    communeId: input.communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: input.newHeadUserId,
                },
                data: {
                    isHead: true,
                },
            });
        });
        return true;
    }
    async getCommunes(input, user) {
        const { ids, query, userId } = input;
        const communes = await this.prisma.commune.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: {
                ...(ids ? { id: { in: ids } } : null),
                ...(query
                    ? {
                        OR: [
                            { id: query },
                            { name: (0, utils_1.toPrismaLocalizationsWhere)(query) },
                        ],
                    }
                    : null),
                ...(userId
                    ? {
                        members: {
                            some: {
                                actorType: client_1.CommuneMemberType.user,
                                actorId: userId,
                            },
                        },
                    }
                    : null),
                deletedAt: user?.isAdmin ? undefined : null,
            },
            select: {
                id: true,
                members: {
                    where: {
                        deletedAt: null,
                    },
                    orderBy: [{ isHead: "desc" }, { createdAt: "asc" }],
                },
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user?.isAdmin,
            },
        });
        const userIds = new Set(communes.flatMap((commune) => commune.members
            .filter((member) => member.isHead &&
            member.actorType === client_1.CommuneMemberType.user)
            .map((member) => member.actorId)));
        const users = await this.prisma.user.findMany({
            where: { id: { in: [...userIds] } },
            select: {
                id: true,
                name: true,
                image: true,
            },
        });
        const userMap = new Map(users.map((user) => [user.id, user]));
        return communes.map((commune) => {
            const headMember = commune.members[0];
            switch (headMember.actorType) {
                case client_1.CommuneMemberType.user: {
                    const headMemberUser = userMap.get(headMember.actorId);
                    return {
                        ...commune,
                        headMember: {
                            actorType: headMember.actorType,
                            actorId: headMember.actorId,
                            name: headMemberUser.name,
                            image: headMemberUser.image?.url ?? null,
                        },
                        memberCount: commune.members.length,
                        image: commune.image?.url ?? null,
                    };
                }
            }
        });
    }
    async getCommune(input, user) {
        const [commune] = await this.getCommunes({
            pagination: { page: 1, size: 1 },
            ids: [input.id],
        }, user);
        return commune;
    }
    async createCommune(input, user) {
        if (!user.isAdmin) {
            if (input.headUserId && input.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_future_head_user"));
            }
        }
        return await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: client_1.CommuneMemberType.user,
                        actorId: input.headUserId ?? user.id,
                        isHead: true,
                    },
                },
                name: {
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                description: {
                    create: (0, utils_1.toPrismaLocalizations)(input.description, "description"),
                },
            },
        });
    }
    async updateCommune(input, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: user.isAdmin ? undefined : null,
            },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
        }
        return await this.prisma.commune.update({
            where: { id: input.id },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                description: input.description && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.description, "description"),
                },
            },
        });
    }
    async updateCommuneImage(communeId, file, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: communeId,
                deletedAt: null,
            },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin && !this.communeCore.isHeadMember(commune, user.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        const imageUrl = await this.minioService.uploadImage(file, "commune", communeId);
        const image = await this.prisma.image.create({
            data: {
                url: imageUrl,
            },
        });
        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                imageId: image.id,
            },
        });
    }
    async deleteCommuneImage(communeId, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: communeId },
            include: {
                members: {
                    where: { isHead: true },
                },
            },
        });
        const isHeadMember = commune.members.some((member) => member.actorType === "user" && member.actorId === user.id);
        if (!user.isAdmin && !isHeadMember) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                imageId: null,
            },
        });
    }
    async deleteCommune(input, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: null,
            },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin && !this.communeCore.isHeadMember(commune, user.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
        }
        await this.prisma.$transaction(async (trx) => {
            const now = new Date();
            await trx.commune.update({
                where: { id: input.id },
                data: { deletedAt: now },
            });
            await trx.communeMember.updateMany({
                where: { communeId: input.id },
                data: { deletedAt: now },
            });
        });
    }
};
exports.CommuneService = CommuneService;
exports.CommuneService = CommuneService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService,
        commune_core_1.CommuneCore])
], CommuneService);
//# sourceMappingURL=commune.service.js.map