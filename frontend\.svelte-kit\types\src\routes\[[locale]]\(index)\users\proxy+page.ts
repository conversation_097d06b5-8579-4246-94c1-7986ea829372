// @ts-nocheck
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const users = await api.user.list.get({}, { fetch, ctx: { url } });

  return {
    users,
    isHasMoreUsers: users.length === Consts.PAGE_SIZE,
  };
};
