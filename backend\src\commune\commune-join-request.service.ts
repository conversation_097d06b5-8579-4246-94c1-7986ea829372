import { Common, Commune } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneJoinRequestStatus, CommuneMemberType } from "@prisma/client";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";

@Injectable()
export class CommuneJoinRequestService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly communeCore: CommuneCore,
    ) {}

    async getJoinRequests(
        input: Commune.GetCommuneJoinRequestsInput,
        user: CurrentUser,
    ) {
        const joinRequests = await this.prisma.communeJoinRequest.findMany({
            ...toPrismaPagination(input.pagination),
            where: Object.assign(
                { deletedAt: null },
                input.communeId
                    ? { communeId: input.communeId }
                    : { userId: user.id },
            ),
            orderBy: {
                createdAt: "desc",
            },
        });

        return joinRequests;
    }

    async createJoinRequest(
        input: Commune.CreateCommuneJoinRequestInput,
        user: CurrentUser,
    ) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: input.communeId },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (user.id !== input.userId) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_self"),
                );
            }
        }

        if (this.communeCore.isMember(commune, input.userId)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        const existingJoinRequest =
            await this.prisma.communeJoinRequest.findFirst({
                where: {
                    communeId: input.communeId,
                    userId: input.userId,
                    status: CommuneJoinRequestStatus.pending,
                    deletedAt: null,
                },
            });

        if (existingJoinRequest) {
            return existingJoinRequest;
        }

        const joinRequest = await this.prisma.communeJoinRequest.create({
            data: {
                communeId: input.communeId,
                userId: input.userId,
            },
        });

        return joinRequest;
    }

    async deleteJoinRequest(input: Common.ObjectWithId, user: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        if (!user.isAdmin) {
            if (joinRequest.userId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_join_request_target"),
                );
            }
        }

        await this.prisma.communeJoinRequest.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async acceptJoinRequest(input: Common.ObjectWithId, user: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: joinRequest.communeId },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: joinRequest.communeId,
                    actorType: CommuneMemberType.user,
                    actorId: joinRequest.userId,
                    isHead: false,
                },
            });

            await this.prisma.communeJoinRequest.update({
                where: { id: input.id },
                data: {
                    status: CommuneJoinRequestStatus.accepted,
                },
            });
        });

        return true;
    }

    async rejectJoinRequest(input: Common.ObjectWithId, user: CurrentUser) {
        const joinRequest =
            await this.prisma.communeJoinRequest.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (joinRequest.status !== CommuneJoinRequestStatus.pending) {
            throw new ForbiddenException(
                ...getError("join_request_must_be_pending"),
            );
        }

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: joinRequest.communeId },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        await this.prisma.communeJoinRequest.update({
            where: { id: input.id },
            data: {
                status: CommuneJoinRequestStatus.rejected,
            },
        });

        return true;
    }
}
