html {
  overflow-y: scroll;
  /* Always show vertical scrollbar to prevent layout shifts */
  scrollbar-width: thin;
  /* For Firefox - make scrollbar thinner */
}

/* Customize scrollbar for WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent;
  /* Track background */
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  /* Scrollbar color */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
  /* Scrollbar color on hover */
}

.responsive-container {
  width: 60%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 767.98px) {
  .responsive-container {
    width: 100%;
  }
}

/* Override Bootstrap container widths to make them 25% wider */
@media (min-width: 576px) {

  .app .container-sm,
  .app .container {
    max-width: 675px;
  }
}

@media (min-width: 768px) {

  .app .container-md,
  .app .container-sm,
  .app .container {
    max-width: 900px;
  }
}

@media (min-width: 992px) {

  .app .container-lg,
  .app .container-md,
  .app .container-sm,
  .app .container {
    max-width: 1200px;
  }
}

@media (min-width: 1200px) {

  .app .container-xl,
  .app .container-lg,
  .app .container-md,
  .app .container-sm,
  .app .container {
    max-width: 1425px;
  }
}

@media (min-width: 1400px) {

  .app .container-xxl,
  .app .container-xl,
  .app .container-lg,
  .app .container-md,
  .app .container-sm,
  .app .container {
    max-width: 1650px;
  }
}